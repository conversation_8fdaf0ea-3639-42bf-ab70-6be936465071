-- 創建 iCook RAG 系統所需的資料庫表格
-- 執行前請確保已連接到 PostgreSQL 資料庫

-- 1. 主食譜表
CREATE TABLE IF NOT EXISTS recipes_cleaned (
    id INTEGER PRIMARY KEY,
    vege_name VARCHAR(50) NOT NULL,
    食譜名稱 TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 預覽食材表
CREATE TABLE IF NOT EXISTS preview_ingredients (
    id INTEGER NOT NULL,
    preview_tag TEXT NOT NULL,
    vege_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id) REFERENCES recipes_cleaned(id) ON DELETE CASCADE
);

-- 3. 詳細食材表
CREATE TABLE IF NOT EXISTS detailed_ingredients (
    id INTEGER NOT NULL,
    ingredient_name VARCHAR(200) NOT NULL,
    quantity VARCHAR(50),
    unit VARCHAR(20),
    vege_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id) REFERENCES recipes_cleaned(id) ON DELETE CASCADE
);

-- 4. 製作步驟表
CREATE TABLE IF NOT EXISTS recipe_steps (
    id INTEGER NOT NULL,
    step_no INTEGER NOT NULL,
    description TEXT NOT NULL,
    vege_name VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id) REFERENCES recipes_cleaned(id) ON DELETE CASCADE,
    PRIMARY KEY (id, step_no)
);

-- 5. 食譜分類表（素食/葷食）
CREATE TABLE IF NOT EXISTS recipe_classifications (
    id INTEGER PRIMARY KEY,
    diet VARCHAR(20) NOT NULL CHECK (diet IN ('vegetarian', 'non_vegetarian')),
    uses_pork BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id) REFERENCES recipes_cleaned(id) ON DELETE CASCADE
);

-- 創建索引以提升查詢效能
CREATE INDEX IF NOT EXISTS idx_preview_ingredients_id ON preview_ingredients(id);
CREATE INDEX IF NOT EXISTS idx_preview_ingredients_tag ON preview_ingredients(preview_tag);
CREATE INDEX IF NOT EXISTS idx_detailed_ingredients_id ON detailed_ingredients(id);
CREATE INDEX IF NOT EXISTS idx_detailed_ingredients_name ON detailed_ingredients(ingredient_name);
CREATE INDEX IF NOT EXISTS idx_recipe_steps_id ON recipe_steps(id);
CREATE INDEX IF NOT EXISTS idx_recipe_classifications_diet ON recipe_classifications(diet);
CREATE INDEX IF NOT EXISTS idx_recipes_vege_name ON recipes_cleaned(vege_name);

-- 創建視圖以便於查詢
CREATE OR REPLACE VIEW recipe_summary AS
SELECT 
    r.id,
    r.vege_name,
    r.食譜名稱,
    rc.diet,
    rc.uses_pork,
    COUNT(DISTINCT di.ingredient_name) as ingredient_count,
    COUNT(DISTINCT rs.step_no) as step_count
FROM recipes_cleaned r
LEFT JOIN recipe_classifications rc ON r.id = rc.id
LEFT JOIN detailed_ingredients di ON r.id = di.id
LEFT JOIN recipe_steps rs ON r.id = rs.id
GROUP BY r.id, r.vege_name, r.食譜名稱, rc.diet, rc.uses_pork;

COMMENT ON TABLE recipes_cleaned IS '主食譜表，儲存基本食譜資訊';
COMMENT ON TABLE preview_ingredients IS '預覽食材表，儲存食譜的簡化食材標籤';
COMMENT ON TABLE detailed_ingredients IS '詳細食材表，儲存完整的食材資訊包含份量和單位';
COMMENT ON TABLE recipe_steps IS '製作步驟表，儲存食譜的詳細製作步驟';
COMMENT ON TABLE recipe_classifications IS '食譜分類表，儲存素食/葷食分類和豬肉使用資訊';
