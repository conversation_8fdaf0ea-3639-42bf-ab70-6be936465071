import pandas as pd
from sqlalchemy import create_engine

# --- 根據您的 docker-compose.yml 填寫的資訊 ---
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "lorraine"  # 和使用者名稱相同
DB_USER = "lorraine"
DB_PASS = "0000"
DB = "postgres"


def get_data_from_db():
    """使用 SQLAlchemy 和 Pandas 從您的資料庫讀取資料"""
    print("🚀 準備連線到您的 PostgreSQL 資料庫...")

    try:
        # 建立資料庫連線 URL
        db_url = f"postgresql+psycopg2://{DB_NAME}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB}"

        # 建立 SQLAlchemy 引擎
        engine = create_engine(db_url)

        # !! 請將 'your_table_name' 換成您實際的資料表名稱 !!
        query = "SELECT * FROM ingredient;"

        # 使用 pandas 直接從 SQL 查詢讀取資料到 DataFrame
        print(f"正在執行查詢: {query}")
        df = pd.read_sql(query, engine)

        print("\n✅ 成功獲取資料！")
        print("資料預覽 (前5筆):")
        print(df.head())

        return df

    except Exception as e:
        print(f"\n❌ 連接或讀取資料時發生錯誤: {e}")
        print("請檢查：")
        print("1. Docker 容器是否已透過 'docker-compose up -d' 成功啟動？")
        print("2. 'your_table_name' 是否正確？")
        return None


if __name__ == "__main__":
    # 執行函式來獲取資料
    training_dataframe = get_data_from_db()

    if training_dataframe is not None:
        print(
            "\n🎉 現在您可以使用 'training_dataframe' 這個 Pandas DataFrame 來進行模型訓練了！"
        )
