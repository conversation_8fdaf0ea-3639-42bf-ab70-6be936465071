# iCook RAG 資料庫設置指南

## 📋 概述
本指南說明如何將 iCook RAG 系統從 CSV 檔案模式轉換為 PostgreSQL 資料庫模式，以提升資料查詢效能和管理便利性。

## 🛠️ 前置需求

### 1. PostgreSQL 安裝
- 下載並安裝 PostgreSQL：https://www.postgresql.org/download/
- 確保 PostgreSQL 服務正在運行
- 記錄資料庫連接資訊（主機、埠號、使用者名稱、密碼）

### 2. Python 依賴套件
```bash
pip install -r scripts/requirements.txt
```

## 🗄️ 資料庫設置步驟

### 步驟 1：創建資料庫表格
```bash
# 使用 psql 命令列工具執行 SQL 腳本
psql -h localhost -U lorraine -d postgres -f scripts/create_database_tables.sql

# 或者使用 pgAdmin 等圖形化工具執行 scripts/create_database_tables.sql
```

### 步驟 2：匯入 CSV 資料到資料庫
```bash
cd scripts
python import_csv_to_db.py
```

### 步驟 3：驗證資料匯入
```sql
-- 檢查各表的資料筆數
SELECT 'recipes_cleaned' as table_name, COUNT(*) as count FROM recipes_cleaned
UNION ALL
SELECT 'preview_ingredients', COUNT(*) FROM preview_ingredients
UNION ALL
SELECT 'detailed_ingredients', COUNT(*) FROM detailed_ingredients
UNION ALL
SELECT 'recipe_steps', COUNT(*) FROM recipe_steps
UNION ALL
SELECT 'recipe_classifications', COUNT(*) FROM recipe_classifications;
```

## 🔧 資料庫連接設定

### 修改連接參數
在 `scripts/search_and_retrieve_recipes.py` 中修改資料庫連接設定：

```python
def get_db_connection():
    return psycopg2.connect(
        host="your_host",        # 預設: localhost
        database="your_db",      # 預設: postgres
        user="your_username",    # 預設: lorraine
        password="your_password", # 預設: 0000
        port=5432               # 預設: 5432
    )
```

## 📊 資料庫表格結構

### 1. recipes_cleaned (主食譜表)
- `id`: 食譜唯一識別碼
- `vege_name`: 蔬菜分類名稱
- `食譜名稱`: 食譜標題

### 2. preview_ingredients (預覽食材表)
- `id`: 食譜 ID (外鍵)
- `preview_tag`: 預覽食材標籤
- `vege_name`: 蔬菜分類名稱

### 3. detailed_ingredients (詳細食材表)
- `id`: 食譜 ID (外鍵)
- `ingredient_name`: 食材名稱
- `quantity`: 份量
- `unit`: 單位
- `vege_name`: 蔬菜分類名稱

### 4. recipe_steps (製作步驟表)
- `id`: 食譜 ID (外鍵)
- `step_no`: 步驟編號
- `description`: 步驟描述
- `vege_name`: 蔬菜分類名稱

### 5. recipe_classifications (食譜分類表)
- `id`: 食譜 ID (外鍵)
- `diet`: 飲食類型 (vegetarian/non_vegetarian)
- `uses_pork`: 是否使用豬肉

## 🚀 執行 RAG 系統

### 使用資料庫版本
```bash
cd scripts
python search_and_retrieve_recipes.py
```

系統會自動：
1. 嘗試從資料庫載入資料
2. 如果資料庫連接失敗，自動回退到 CSV 檔案模式
3. 顯示載入的資料統計資訊

## 🔍 效能優化

### 索引優化
系統已自動創建以下索引：
- `preview_ingredients` 表的 `id` 和 `preview_tag` 欄位
- `detailed_ingredients` 表的 `id` 和 `ingredient_name` 欄位
- `recipe_steps` 表的 `id` 欄位
- `recipe_classifications` 表的 `diet` 欄位

### 查詢優化視圖
使用 `recipe_summary` 視圖快速獲取食譜摘要資訊：
```sql
SELECT * FROM recipe_summary WHERE diet = 'vegetarian' LIMIT 10;
```

## 🛡️ 備份與還原

### 備份資料庫
```bash
pg_dump -h localhost -U lorraine -d postgres > icook_rag_backup.sql
```

### 還原資料庫
```bash
psql -h localhost -U lorraine -d postgres < icook_rag_backup.sql
```

## ❗ 故障排除

### 常見問題

1. **資料庫連接失敗**
   - 檢查 PostgreSQL 服務是否運行
   - 確認連接參數正確
   - 檢查防火牆設定

2. **匯入資料失敗**
   - 確認 CSV 檔案路徑正確
   - 檢查檔案編碼格式
   - 確認資料庫表格已創建

3. **查詢效能問題**
   - 檢查索引是否正確創建
   - 考慮增加更多索引
   - 分析查詢執行計畫

### 日誌檢查
```bash
# 檢查 PostgreSQL 日誌
tail -f /var/log/postgresql/postgresql-*.log
```

## 📈 系統優勢

### 使用資料庫的好處：
1. **效能提升**: 索引加速查詢
2. **資料完整性**: 外鍵約束確保資料一致性
3. **並發支援**: 多用戶同時存取
4. **備份容易**: 標準資料庫備份工具
5. **擴展性**: 支援大量資料
6. **查詢靈活**: SQL 查詢功能強大

### 與 CSV 模式的比較：
- **載入速度**: 資料庫 > CSV (大量資料時)
- **查詢效能**: 資料庫 >> CSV
- **記憶體使用**: 資料庫 < CSV
- **維護性**: 資料庫 > CSV
